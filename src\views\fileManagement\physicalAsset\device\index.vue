<!--设备资产管理页面-->
<template>
  <div class="device-container">
    <!-- 顶部统计卡片 -->
    <div class="statistics-cards">
      <div class="stat-card">
        <div class="icon-wrapper blue-bg">
          <i class="el-icon-s-data" />
        </div>
        <div class="stat-info">
          <div class="stat-title">设备总数</div>
          <div class="stat-value blue-text">{{ statsData.totalCount || 0 }}<span>台</span></div>
        </div>
      </div>

      <div class="stat-card">
        <div class="icon-wrapper green-bg">
          <i class="el-icon-s-check" />
        </div>
        <div class="stat-info">
          <div class="stat-title">正常设备</div>
          <div class="stat-value green-text">{{ statsData.normalCount || 0 }}<span>台</span></div>
        </div>
      </div>

      <div class="stat-card">
        <div class="icon-wrapper orange-bg">
          <i class="el-icon-warning" />
        </div>
        <div class="stat-info">
          <div class="stat-title">闲置设备</div>
          <div class="stat-value orange-text">{{ statsData.idleCount || 0 }}<span>台</span></div>
        </div>
      </div>

      <div class="stat-card">
        <div class="icon-wrapper red-bg">
          <i class="el-icon-money" />
        </div>
        <div class="stat-info">
          <div class="stat-title">总价值</div>
          <div class="stat-value red-text">{{ statsData.totalValue || 0 }}<span>万元</span></div>
        </div>
      </div>
    </div>

    <!-- 搜索表单 -->
    <div class="search-form">
      <el-form :inline="true" :model="listQuery" class="form-inline" label-width="100px">
        <el-form-item label="资产名称:">
          <el-input v-model="listQuery.zcfaAssetsName" placeholder="请输入资产名称" class="inputW" />
        </el-form-item>
        <el-form-item label="产权单位:">
          <el-input v-model="listQuery.companyName" placeholder="请输入产权单位" class="inputW" />
        </el-form-item>
        <el-form-item label="资产状态:">
          <el-select v-model="listQuery.zcfaAssetsState" placeholder="请选择资产状态" class="inputW" clearable>
            <el-option label="正常" value="正常" />
            <el-option label="闲置" value="闲置" />
            <el-option label="报废" value="报废" />
            <el-option label="维修" value="维修" />
          </el-select>
        </el-form-item>
        <el-form-item label="资产类型:">
          <el-select v-model="listQuery.zcfaType" placeholder="请选择资产类型" class="inputW" clearable>
            <el-option label="设备" value="设备" />
            <el-option label="仪器" value="仪器" />
            <el-option label="工具" value="工具" />
          </el-select>
        </el-form-item>
        <el-form-item label="制造厂商:">
          <el-input v-model="listQuery.zcfaManufactor" placeholder="请输入制造厂商" class="inputW" />
        </el-form-item>
        <el-form-item label="型号:">
          <el-input v-model="listQuery.zcfaModel" placeholder="请输入型号" class="inputW" />
        </el-form-item>
        <el-form-item label="生产日期:">
          <el-date-picker v-model="productDateRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" style="width: 240px;" @change="handleProductDateChange" />
        </el-form-item>
        <el-form-item label="入账日期:">
          <el-date-picker v-model="recordDateRange" type="daterange" range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期" style="width: 240px;" @change="handleRecordDateChange" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleFilter">查询</el-button>
          <el-button @click="handleExport">导出</el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 数据表格 -->
    <el-table :data="list" :height="280" border style="width: 100%;margin-bottom: 20px;" v-loading="listLoading" row-key="zcfaId">
      <el-table-column type="index" label="序号" width="60" align="center" />
      <el-table-column prop="zcfaAssetsName" label="资产名称" width="150" show-overflow-tooltip />
      <el-table-column prop="companyName" label="产权单位" width="120" show-overflow-tooltip />
      <el-table-column prop="zcfaAssetsState" label="资产状态" width="100" align="center">
        <template slot-scope="scope">
          <el-tag :type="getStatusTagType(scope.row.zcfaAssetsState)" size="small">
            {{ scope.row.zcfaAssetsState }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="zcfaType" label="资产类型" width="100" align="center" />
      <el-table-column prop="zcfaManufactor" label="制造厂商" width="120" show-overflow-tooltip />
      <el-table-column prop="zcfaModel" label="型号" width="120" show-overflow-tooltip />
      <el-table-column prop="zcfaSerialNumber" label="序号" width="100" align="center" />
      <el-table-column prop="zcfaBookValue" label="账面原值(万元)" width="130" align="right">
        <template slot-scope="scope">
          {{ scope.row.zcfaBookValue ? Number(scope.row.zcfaBookValue).toFixed(2) : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="zcfaNetbookValue" label="账面净值(万元)" width="130" align="right">
        <template slot-scope="scope">
          {{ scope.row.zcfaNetbookValue ? Number(scope.row.zcfaNetbookValue).toFixed(2) : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="zcfaProductDate" label="生产日期" width="110" align="center">
        <template slot-scope="scope">
          {{ scope.row.zcfaProductDate ? scope.row.zcfaProductDate.split(' ')[0] : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="zcfaRecordDate" label="入账日期" width="110" align="center">
        <template slot-scope="scope">
          {{ scope.row.zcfaRecordDate ? scope.row.zcfaRecordDate.split(' ')[0] : '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="zcfaStoragePlace" label="存放地点" width="120" show-overflow-tooltip />
      <el-table-column label="操作" width="80" fixed="right">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="handleDetail(scope.row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <el-pagination @current-change="handleCurrentChange" @size-change="handleSizeChange" :current-page="currentPage" :page-size="pageSize" :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="total" background />

    <!-- 详情弹窗组件 -->
    <device-detail-dialog ref="deviceDetail" />
  </div>
</template>

<script>
import { getDevicesList, exportDevices, getDevicesStats } from '@/api/device'
import DeviceDetailDialog from './components/DeviceDetailDialog.vue'

export default {
  name: "index",
  components: {
    DeviceDetailDialog
  },
  data () {
    return {
      list: [],
      total: 0,
      listLoading: true,
      currentPage: 1,
      pageSize: 10,
      listQuery: {
        zcfaAssetsName: '',
        companyName: '',
        zcfaAssetsState: '',
        zcfaType: '',
        zcfaManufactor: '',
        zcfaModel: '',
        zcfaProductDateStart: '',
        zcfaProductDateEnd: '',
        zcfaRecordDateStart: '',
        zcfaRecordDateEnd: ''
      },
      productDateRange: [],
      recordDateRange: [],
      statsData: {
        totalCount: 0,
        normalCount: 0,
        idleCount: 0,
        totalValue: 0
      },
    }
  },
  created () {
    this.fetchData()
    this.fetchStatistics()
  },
  methods: {
    fetchData () {
      this.listLoading = true
      const query = {
        ...this.listQuery,
        pageNo: this.currentPage,
        pageSize: this.pageSize
      }

      getDevicesList(query).then(response => {
        if (response && response.data) {
          this.list = response.data.list || []
          this.total = response.data.total || 0
        } else {
          this.list = []
          this.total = 0
        }
        this.listLoading = false
      }).catch(() => {
        this.listLoading = false
        this.$message.error('获取数据失败')
      })
    },

    fetchStatistics () {
      getDevicesStats().then(response => {
        if (response && response.data) {
          this.statsData = {
            totalCount: response.data.totalCount || 0,
            normalCount: response.data.normalCount || 0,
            idleCount: response.data.idleCount || 0,
            totalValue: response.data.totalValue || 0
          }
        }
      }).catch(() => {
        this.$message.error('获取统计数据失败')
      })
    },

    onSearch () {
      this.currentPage = 1
      this.fetchData()
    },

    handleFilter () {
      this.onSearch()
    },
    // resetQuery () {
    //   this.currentPage = 1
    //   this.listQuery = {
    //     zcfaAssetsName: '',
    //     companyName: '',
    //     zcfaAssetsState: '',
    //     zcfaType: '',
    //     zcfaManufactor: '',
    //     zcfaModel: '',
    //     zcfaProductDateStart: '',
    //     zcfaProductDateEnd: '',
    //     zcfaRecordDateStart: '',
    //     zcfaRecordDateEnd: ''
    //   }
    //   this.productDateRange = []
    //   this.recordDateRange = []
    //   this.fetchData()
    // },
    handleProductDateChange (val) {
      if (val && val.length === 2) {
        this.listQuery.zcfaProductDateStart = val[0]
        this.listQuery.zcfaProductDateEnd = val[1]
      } else {
        this.listQuery.zcfaProductDateStart = ''
        this.listQuery.zcfaProductDateEnd = ''
      }
    },
    handleRecordDateChange (val) {
      if (val && val.length === 2) {
        this.listQuery.zcfaRecordDateStart = val[0]
        this.listQuery.zcfaRecordDateEnd = val[1]
      } else {
        this.listQuery.zcfaRecordDateStart = ''
        this.listQuery.zcfaRecordDateEnd = ''
      }
    },
    onExport () {
      const query = {
        ...this.listQuery
      }
      exportDevices(query).then(() => {
        // 处理导出逻辑
        this.$message.success('导出成功')
      }).catch(() => {
        this.$message.error('导出失败')
      })
    },

    handleExport () {
      this.onExport()
    },

    handleDetail (row) {
      this.$refs.deviceDetail.showDialog(row)
    },

    getStatusTagType (status) {
      const statusMap = {
        '正常': 'success',
        '闲置': 'warning',
        '报废': 'danger',
        '维修': 'info'
      }
      return statusMap[status] || 'info'
    },
    handleSizeChange (val) {
      this.pageSize = val
      this.fetchData()
    },

    handleCurrentChange (val) {
      this.currentPage = val
      this.fetchData()
    }
  }
}
</script>

<style lang="scss" scoped>
.inputW {
  width: 200px;
}

.device-container {
  padding: 16px;
  background-color: #f5f7fa;
}

/* 顶部统计卡片样式 */
.statistics-cards {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.stat-card {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-right: 15px;
}

.stat-card:last-child {
  margin-right: 0;
}

.icon-wrapper {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 15px;
}

.icon-wrapper i {
  font-size: 24px;
  color: #fff;
}

.red-bg {
  background-color: rgba(245, 108, 108, 0.2);
}

.orange-bg {
  background-color: rgba(230, 162, 60, 0.2);
}

.blue-bg {
  background-color: rgba(64, 158, 255, 0.2);
}

.green-bg {
  background-color: rgba(103, 194, 58, 0.2);
}

.stat-info {
  flex: 1;
}

.stat-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.stat-value {
  font-size: 20px;
  font-weight: bold;
}

.stat-value span {
  font-size: 14px;
  margin-left: 4px;
}

.red-text {
  color: #f56c6c;
}

.orange-text {
  color: #e6a23c;
}

.blue-text {
  color: #409eff;
}

.green-text {
  color: #67c23a;
}

/* 搜索表单样式 */
.search-form {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;
}

/* 数据表格样式 */
.data-table {
  background-color: #fff;
  padding: 20px;
  border-radius: 4px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}
</style>
